import { useState } from 'react'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@repo/shared/components/ui/card'
import { Label } from '@repo/shared/components/ui/label'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Switch } from '@repo/shared/components/ui/switch'
import ColorPicker from '@repo/shared/components/ui/color-picker'
import { AssetPicker } from '@repo/shared/components/editor/assetsManager'
import { Button } from '@repo/shared/components/ui/button'
import { RefreshCw, Settings } from 'lucide-react'
import { ReactGameConfig, defaultGameConfig } from '../types/config'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@repo/shared/components/ui/dialog'
import CategorizedConfigEditor from './CategorizedConfigEditor'


export default function MainConfigEditor({ config, updateConfig }: GameEditorComponentProps) {
    if (!config) config = {}
    // if (!config.gameEndHandler) config.gameEndHandler = {}

    const [isGameSettingsDialogOpen, setIsGameSettingsDialogOpen] = useState(false)

    const handleConfigChange = (key: keyof ReactGameConfig, value: any) => {
        updateConfig({ ...config, [key]: value })
    }

    const handleResetToDefault = (key: keyof ReactGameConfig) => {
        const defaultValue = defaultGameConfig[key]
        if (defaultValue !== undefined) {
            handleConfigChange(key, defaultValue)
        }
    }

    return (
        <div className="space-y-6">
            {/* Game Configuration Dialog */}
            <Card>
                <CardHeader>
                    <CardTitle>Game Configuration</CardTitle>
                    <CardDescription>Configure appearance and game settings organized by category</CardDescription>
                </CardHeader>
                <CardContent>
                    <Dialog open={isGameSettingsDialogOpen} onOpenChange={setIsGameSettingsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button variant="outline" className="w-full">
                                <Settings className="h-4 w-4 mr-2" />
                                Open Configuration
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Game Configuration</DialogTitle>
                            </DialogHeader>
                            <CategorizedConfigEditor config={config} updateConfig={updateConfig} />
                        </DialogContent>
                    </Dialog>
                </CardContent>
            </Card>

            {/* Main Background Card */}
            <Card>
                <CardHeader>
                    <CardTitle>Main Background</CardTitle>
                    <CardDescription>Configure the main game background</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="flex justify-end mb-2">
                        <Button variant="outline" size="sm" onClick={() => handleResetToDefault('mainBackground')} className="flex items-center gap-1">
                            <RefreshCw size={14} />
                            Reset
                        </Button>
                    </div>

                    <div>
                        <Label className="mb-2 block">Background Image</Label>
                        <AssetPicker
                            onSelect={(assetId) =>
                                handleConfigChange('mainBackground', {
                                    ...config.mainBackground,
                                    asset: { assetId },
                                })
                            }
                            assetUrl={config.mainBackground?.asset}
                            extensions={['png', 'jpg', 'jpeg', 'avif', 'webp', 'svg', 'gif']}
                        />
                    </div>

                    <div>
                        <div className="flex items-center space-x-2 mb-2">
                            <Label className="flex-grow">Background Color</Label>
                            <Switch
                                checked={config.mainBackground?.useBackgroundColor !== false}
                                onCheckedChange={(useBackgroundColor) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        useBackgroundColor,
                                    })
                                }
                            />
                        </div>
                        <div className={`transition-opacity ${config.mainBackground?.useBackgroundColor !== false ? 'opacity-100' : 'opacity-40'}`}>
                            <ColorPicker
                                color={config.mainBackground?.fill || '#faf8ef'}
                                onChange={(backgroundColor) =>
                                    handleConfigChange('mainBackground', {
                                        ...config.mainBackground,
                                        fill: backgroundColor,
                                    })
                                }
                            />
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}
