export class GameConfig {

}

export type ConfigCategory = 'appearance' | 'settings'

export type ConfigEditorType =
        | 'image'
        | 'spin-game-button'
        | 'image-background'
        | '2048-tile'
        | '2048-grid-background'
        | 'empty-2048-tile'
        | 'game-button'
        | 'audio'
        | 'font'
        | 'number'
        | 'lives-handler'
        | '2048-scoreboard-numbers-style'
        | 'text'
        | 'text-style'
        | 'spacing'
        | 'background'
        | 'scoreboard-numbers-style'
        | 'rewards-handler'
        | 'container'
        | 'reward-component-style'
        | 'scratch-overlay'
        | 'sound-switch'
        | 'memory-grid'
        | 'counter'
        | 'memory-card'
        | 'spin-wheel'
        | 'answer-tile'
        | 'question-area-container'
        | 'game-timer'
        | 'question-answer-settings'
        | 'timer-handler'
        | 'animable-sprite'

export type GameConfigKey = {
    category: ConfigCategory
    name?: string
    width?: number
    height?: number
    configEditorType?: ConfigEditorType
    editorSettings?: any
}

// Keep track of metadata for decorated properties
const assetMetadata = new Map<string, GameConfigKey>()

// Cache for initialized class instances
const classInstanceCache = new Map<Function, object>()

export function gameConfigKey<T>(config: GameConfigKey = {category: 'appearance' }) {

     return function (target: undefined, context: ClassFieldDecoratorContext<T, any>) {
         return function (this: T, value: any) {
             console.log('Registering: ', typeof this,  context.name.toString())
             assetMetadata.set(this.constructor.name + '.' + context.name.toString(), {
                 ...config,
             })

             return value
         }
     }

}

// Helper function to get asset dimensions
export function getConfigKeyDefinition<T extends object>(classType: new (...args: any[]) => T, propertyName: keyof any): GameConfigKey | undefined {
    loadClassTypeDecorators(classType)
    const fullPropertyName = classType.name + '.' + String(propertyName)

    return assetMetadata.get(fullPropertyName)
}

function loadClassTypeDecorators(classType: any) {
    let instance = classInstanceCache.get(classType)
    if (!instance) {
        instance = new classType()
        classInstanceCache.set(classType, instance)
    }
}

// Helper function to get all config keys by category
export function getConfigKeysByCategory<T extends object>(classType: new (...args: any[]) => T, category: ConfigCategory): string[] {
    loadClassTypeDecorators(classType)
    const keys: string[] = []

    assetMetadata.forEach((metadata, fullPropertyName) => {
        if (fullPropertyName.startsWith(classType.name + '.') && metadata.category === category) {
            const propertyName = fullPropertyName.substring(classType.name.length + 1)
            keys.push(propertyName)
        }
    })

    return keys
}

// Helper function to get config values by category
export function getConfigByCategory<T extends object>(config: T, classType: new (...args: any[]) => T, category: ConfigCategory): Partial<T> {
    const keys = getConfigKeysByCategory(classType, category)
    const result: Partial<T> = {}

    for (const key of keys) {
        if (key in config) {
            (result as any)[key] = (config as any)[key]
        }
    }

    return result
}

// Helper function to update config values by category
export function updateConfigByCategory<T extends object>(config: T, classType: new (...args: any[]) => T, category: ConfigCategory, updates: Partial<T>): T {
    const keys = getConfigKeysByCategory(classType, category)
    const result = { ...config }

    for (const key of keys) {
        if (key in updates) {
            (result as any)[key] = (updates as any)[key]
        }
    }

    return result
}