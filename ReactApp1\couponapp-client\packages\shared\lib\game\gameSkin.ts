/**
 * A skin: just a name and a set of partial overrides for your game's config
 */
export type GameSkin<T extends object> = {
  name: string;
  id: string;
  gameId: string; // The game module ID this skin can be applied to
  configOverrides: Partial<T>;
};

/**
 * Check if a skin can be applied to a specific game.
 * Returns true if the skin's gameId matches the provided gameId.
 */
export function canApplySkin<T extends object>(
  gameId: string,
  skin: GameSkin<T>
): boolean {
  return skin.gameId === gameId;
}

/**
 * Filter an array of skins to only include those compatible with a specific game.
 * Returns an array of skins that can be applied to the given gameId.
 */
export function getCompatibleSkins<T extends object>(
  gameId: string,
  skins: GameSkin<T>[]
): GameSkin<T>[] {
  return skins.filter(skin => canApplySkin(gameId, skin));
}

/**
 * Apply a skin to a config object.
 * Returns a new T where every property in skin.configOverrides
 * is merged (deeply) into the source config.
 *
 * @param baseConfig - The base configuration to apply the skin to
 * @param skin - The skin to apply
 * @param gameId - Optional game ID to validate against. If provided, will throw if skin doesn't match.
 */
export function applySkin<T extends object>(
  baseConfig: T,
  skin: GameSkin<T>,
  gameId?: string
): T {
  if (gameId && !canApplySkin(gameId, skin)) {
    throw new Error(`Skin "${skin.name}" (ID: ${skin.id}) cannot be applied to game "${gameId}". This skin is only compatible with game "${skin.gameId}".`);
  }

  return deepMerge(baseConfig, skin.configOverrides);
}

/**
 * Recursively merge `source` into `target`.  Shallow-copies target first,
 * then for every own-key in source:
 *   • if both target[key] and source[key] are plain objects, merge recursively
 *   • otherwise (including arrays), take source[key] (unless it's undefined)
 */
function deepMerge<T extends object>(
  target: T,
  source: Partial<T>
): T {
  // start with a shallow copy of target
  const result = { ...target } as any;

  for (const key of Object.keys(source) as Array<keyof T>) {
    const srcVal = source[key];
    if (srcVal === undefined) {
      // skip undefined overrides
      continue;
    }

    const tgtVal = (target as any)[key];
    if (isObject(tgtVal) && isObject(srcVal)) {
      // both are objects: recurse
      result[key] = deepMerge(tgtVal, srcVal as any);
    } else {
      // primitive, array, function, null, etc.
      result[key] = srcVal;
    }
  }

  return result as T;
}

/** true for plain objects (not arrays, not null) */
function isObject(val: any): val is object {
  return val !== null && typeof val === "object" && !Array.isArray(val);
}
