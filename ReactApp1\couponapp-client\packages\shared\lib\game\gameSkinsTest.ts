import { GameSkin, canApplySkin, getCompatibleSkins, applySkin } from "./gameSkin";



 const QuizGameTestSkin: GameSkin<any> = {
    id: "1",
    name: "This is a test skin",
    gameId: "quiz-game", // This skin is only compatible with the quiz game

    configOverrides: {
        answerButtonCorrect: {
            textConfig: {
                text: null, // Will be set from code
                style: {
                    fontFamily_tFontFamily: 'Poppins',
                    fontSize: 16,
                    fill: '#ffaaff',
                    isVisible: true,
                    textAlign: 'center',
                },
            },
            fill: '#22c55e', // green-500
            useBackgroundColor: true,
            borderRadius: 12,
        },

        answerButtonIncorrect: {
            textConfig: {
                text: null, // Will be set from code
                style: {
                    fontFamily_tFontFamily: 'Poppins',
                    fontSize: 16,
                    fill: '#ffddee',
                    isVisible: true,
                    textAlign: 'center',
                },
            },
            fill: '#ef4444', // red-500
            useBackgroundColor: true,
            borderRadius: 12,
        },

        questionContainer: {
            fill: '#ff00ff', // indigo-800
            useBackgroundColor: true,
            borderRadius: 16,
            padding: {
                top: 24,
                right: 24,
                bottom: 24,
                left: 24,
            },
            maxWidth: 450,
        },
    }
}

// Example skin for a different game to demonstrate filtering
const MemoryGameTestSkin: GameSkin<any> = {
    id: "2",
    name: "Memory Game Dark Theme",
    gameId: "memory", // This skin is only compatible with the memory game
    configOverrides: {
        mainBackground: {
            fill: '#1a1a1a',
            useBackgroundColor: true,
        },
        cardBack: {
            fill: '#333333',
            useBackgroundColor: true,
            borderRadius: 8,
        }
    }
}

export const skinsDirectory: GameSkin<any>[] = [
    QuizGameTestSkin,
    MemoryGameTestSkin
]

// Example usage and testing functions
export function testSkinCompatibility() {
    console.log('Testing skin compatibility...')

    // Test getting compatible skins for quiz game
    const quizSkins = getCompatibleSkins('quiz-game', skinsDirectory)
    console.log('Quiz game compatible skins:', quizSkins.map(s => s.name))

    // Test getting compatible skins for memory game
    const memorySkins = getCompatibleSkins('memory', skinsDirectory)
    console.log('Memory game compatible skins:', memorySkins.map(s => s.name))

    // Test getting compatible skins for non-existent game
    const nonExistentSkins = getCompatibleSkins('non-existent-game', skinsDirectory)
    console.log('Non-existent game compatible skins:', nonExistentSkins.map(s => s.name))

    // Test individual skin compatibility
    console.log('Can apply QuizGameTestSkin to quiz-game:', canApplySkin('quiz-game', QuizGameTestSkin))
    console.log('Can apply QuizGameTestSkin to memory:', canApplySkin('memory', QuizGameTestSkin))

    // Test applying skin with validation
    try {
        const testConfig = { someProperty: 'test' }
        const result = applySkin(testConfig, QuizGameTestSkin, 'quiz-game')
        console.log('Successfully applied skin to quiz game')
    } catch (error) {
        console.error('Failed to apply skin:', error)
    }

    // Test applying incompatible skin
    try {
        const testConfig = { someProperty: 'test' }
        const result = applySkin(testConfig, QuizGameTestSkin, 'memory')
        console.log('This should not happen - incompatible skin was applied')
    } catch (error) {
        console.log('Correctly prevented incompatible skin application:', error.message)
    }
}