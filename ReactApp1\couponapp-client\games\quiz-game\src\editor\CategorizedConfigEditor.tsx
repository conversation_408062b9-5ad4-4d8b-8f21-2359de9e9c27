import { useState } from 'react'
import { GameEditorComponentProps } from '@repo/shared/lib/game/game'
import { Button } from '@repo/shared/components/ui/button'
import { ReactGameConfig } from '../types/config'
import { getConfigKeysByCategory } from '@repo/shared/lib/game/gameConfig'
import { ConfigKeyEditor } from './ConfigKeyEditor'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@repo/shared/components/ui/card'

type CategoryType = 'appearance' | 'settings'

interface Category {
    id: CategoryType
    label: string
    description: string
}

const categories: Category[] = [
    {
        id: 'appearance',
        label: 'Appearance',
        description: 'Visual styling that can be exported to skins'
    },
    {
        id: 'settings',
        label: 'Settings',
        description: 'Game mechanics and logic configuration'
    }
]

export default function CategorizedConfigEditor({ config, updateConfig }: GameEditorComponentProps) {
    if (!config) config = {}

    const [selectedCategory, setSelectedCategory] = useState<CategoryType>('appearance')

    const handleConfigChange = (updates: any) => {
        updateConfig({ ...config, ...updates })
    }

    const renderCategoryContent = () => {
        const configKeys = getConfigKeysByCategory(ReactGameConfig, selectedCategory)
        
        if (configKeys.length === 0) {
            return (
                <div className="text-center text-muted-foreground py-8">
                    No configuration options available for this category.
                </div>
            )
        }

        return (
            <div className="space-y-6">
                {configKeys.map((configKey) => (
                    <Card key={configKey}>
                        <CardHeader>
                            <CardTitle className="text-base">
                                {configKey.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <ConfigKeyEditor
                                configKey={configKey as keyof ReactGameConfig}
                                config={config}
                                updateConfig={handleConfigChange}
                            />
                        </CardContent>
                    </Card>
                ))}
            </div>
        )
    }

    return (
        <div className="flex h-[600px] gap-4">
            {/* Left Panel - Categories */}
            <div className="w-80 border-r pr-4">
                <div className="space-y-2">
                    {categories.map((category) => (
                        <Button
                            key={category.id}
                            variant={selectedCategory === category.id ? 'default' : 'ghost'}
                            className="w-full justify-start text-left h-auto p-3"
                            onClick={() => setSelectedCategory(category.id)}
                        >
                            <div>
                                <div className="font-medium">{category.label}</div>
                                <div className="text-xs text-muted-foreground mt-1">
                                    {category.description}
                                </div>
                            </div>
                        </Button>
                    ))}
                </div>
            </div>

            {/* Right Panel - Category Content */}
            <div className="flex-1 overflow-y-auto">
                <div className="p-6">
                    <div className="mb-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="font-semibold text-lg">
                                    {categories.find(c => c.id === selectedCategory)?.label}
                                </h3>
                                <p className="text-sm text-muted-foreground">
                                    {categories.find(c => c.id === selectedCategory)?.description}
                                </p>
                            </div>
                        </div>
                    </div>
                    {renderCategoryContent()}
                </div>
            </div>
        </div>
    )
}
